/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.agenda.excel;


import lombok.Data;

import java.time.LocalDate;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 会议议程表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class AgendaExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 开始时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("开始时间")
	private String startTime;
	/**
	 * 结束时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("结束时间")
	private String endTime;
	/**
	 * 议题/主题
	 */
	@ColumnWidth(20)
	@ExcelProperty("议题/主题")
	private String topic;
	/**
	 * 演讲人
	 */
	@ColumnWidth(20)
	@ExcelProperty("演讲人")
	private String speaker;
	/**
	 * 会场
	 */
	@ColumnWidth(20)
	@ExcelProperty("会场")
	private String venue;
	/**
	 * 议程描述
	 */
	@ColumnWidth(20)
	@ExcelProperty("议程描述")
	private String description;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private Long id;
	/**
	 * 会议日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("会议日期")
	private LocalDate date;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private Integer isDeleted;

}
